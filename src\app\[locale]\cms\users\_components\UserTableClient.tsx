"use client";

import { useTranslations } from "next-intl";
import { Badge } from "@/components/ui/badge";
import Text from "@/components/Text";
import { Button, buttonVariants } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import { Link } from "@/i18n/navigation";
import {
  Table,
  TableHeader,
  TableBody,
  TableRow,
  TableHead,
  TableCell,
} from "@/components/ui/table";
import {
  banUser,
  unbanUser,
  revokeAllUserSessions,
  removeUser,
} from "../actions"; // Server actions
import { useQueryStates } from "nuqs"; // Changed from useQueryState
import {
  userPaginationSearchParams,
  userPaginationUrlKeys,
  serializeUserPaginationParams,
} from "../userSearchParams"; // Import centralized nuqs definitions
import { UserWithRole } from "better-auth/plugins";
import { TFunctionCMS } from "@/i18n/types";

// Helper function (can be moved to utils if shared)
function getUserStatus(
  user: UserWithRole,
  t: ReturnType<typeof useTranslations<"CMS.users">>,
): { text: string; variant: "default" | "secondary" | "destructive" } {
  if (user.banned) {
    if (user.banExpires) {
      const expiryDate = new Date(user.banExpires);
      if (expiryDate > new Date()) {
        return {
          text: t("table.statusBannedUntil", {
            date: expiryDate.toLocaleDateString(),
          }),
          variant: "destructive",
        };
      } else {
        return { text: t("table.statusActive"), variant: "default" };
      }
    }
    return { text: t("table.statusBanned"), variant: "destructive" };
  }
  return { text: t("table.statusActive"), variant: "default" };
}

interface UserTableClientProps {
  initialUsers: UserWithRole[];
  totalUsers: number;
  initialPage: number;
  initialLimit: number;
  locale: string;
}

export default function UserTableClient({
  initialUsers,
  totalUsers,
}: UserTableClientProps) {
  const t = useTranslations("CMS.users");

  const [searchParams] = useQueryStates(userPaginationSearchParams, {
    urlKeys: userPaginationUrlKeys,
    shallow: false,
    history: "replace",
  });

  const currentPage = searchParams.page;
  const currentLimit = searchParams.limit;

  const totalPages = Math.ceil(totalUsers / currentLimit);

  if (initialUsers.length === 0 && currentPage === 1) {
    return (
      <div className="flex flex-col items-center justify-center py-12">
        <Text as="h3" size="lg" className="mb-2 font-semibold">
          {t("table.noUsers")}
        </Text>
        <Text as="p" size="sm" className="text-muted-foreground text-center">
          {t("table.noUsersDescription")}
        </Text>
      </div>
    );
  }

  if (initialUsers.length === 0 && currentPage > 1) {
    return (
      <div className="flex flex-col items-center justify-center py-12">
        <Text as="h3" size="lg" className="mb-2 font-semibold">
          {t("table.noUsersOnPage")}
        </Text>
        <Link
          href={serializeUserPaginationParams({ page: 1, limit: currentLimit })}
          className={cn(buttonVariants({ variant: "outline" }))}
        >
          {t("actions.goToFirstPage")}
        </Link>
      </div>
    );
  }

  return (
    <>
      <div className="border-border overflow-x-auto rounded-lg border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>{t("table.name")}</TableHead>
              <TableHead>{t("table.email")}</TableHead>
              <TableHead>{t("table.role")}</TableHead>
              <TableHead>{t("table.status")}</TableHead>
              <TableHead>{t("table.actions")}</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {initialUsers.map((user) => {
              const status = getUserStatus(user, t);
              return (
                <TableRow key={user.id} className="hover:bg-muted/20">
                  <TableCell className="font-medium">
                    {user.name || t("table.notAvailable")}
                  </TableCell>
                  <TableCell>{user.email}</TableCell>
                  <TableCell>
                    <Badge
                      variant={user.role === "admin" ? "default" : "secondary"}
                    >
                      {user.role}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <Badge variant={status.variant}>{status.text}</Badge>
                  </TableCell>
                  {/* <TableCell>
                    <div className="flex items-center space-x-1 md:space-x-2">
                      {user.banned ? (
                        <form action={unbanUser}>
                          <input type="hidden" name="userId" value={user.id} />
                          <input type="hidden" name="locale" value={locale} />
                          <Button
                            type="submit"
                            variant="outline"
                            size="sm"
                            className="text-xs"
                          >
                            {t("actions.unban")}
                          </Button>
                        </form>
                      ) : (
                        <form action={banUser}>
                          <input type="hidden" name="userId" value={user.id} />
                          <input type="hidden" name="locale" value={locale} />
                          <Button
                            type="submit"
                            variant="outline"
                            size="sm"
                            className="text-xs"
                          >
                            {t("actions.ban")}
                          </Button>
                        </form>
                      )}
                      <form action={revokeAllUserSessions}>
                        <input type="hidden" name="userId" value={user.id} />
                        <input type="hidden" name="locale" value={locale} />
                        <Button
                          type="submit"
                          variant="ghost"
                          size="sm"
                          className="text-xs"
                        >
                          {t("actions.revokeSessions")}
                        </Button>
                      </form>
                      <form action={removeUser}>
                        <input type="hidden" name="userId" value={user.id} />
                        <input type="hidden" name="locale" value={locale} />
                        <Button
                          type="submit"
                          variant="destructive"
                          size="sm"
                          className="text-xs"
                        >
                          {t("actions.remove")}
                        </Button>
                      </form>
                    </div>
                  </TableCell> */}
                </TableRow>
              );
            })}
          </TableBody>
        </Table>
      </div>

      {totalPages > 0 && (
        <div className="mt-6 flex items-center justify-between">
          <Link
            href={serializeUserPaginationParams({
              ...searchParams,
              page: Math.max(1, currentPage - 1),
            })}
            className={cn(
              buttonVariants({ variant: "outline" }),
              currentPage === 1 ? "pointer-events-none opacity-50" : "",
            )}
            aria-disabled={currentPage === 1}
            tabIndex={currentPage === 1 ? -1 : undefined}
            prefetch={false}
          >
            {t("actions.previous")}
          </Link>
          <Text size="sm" className="text-muted-foreground">
            {t("actions.pageInfo", { currentPage, totalPages, totalUsers })}
          </Text>
          <Link
            href={serializeUserPaginationParams({
              ...searchParams,
              page: Math.min(totalPages, currentPage + 1),
            })}
            className={cn(
              buttonVariants({ variant: "outline" }),
              currentPage === totalPages || totalPages === 0
                ? "pointer-events-none opacity-50"
                : "",
            )}
            aria-disabled={currentPage === totalPages || totalPages === 0}
            tabIndex={
              currentPage === totalPages || totalPages === 0 ? -1 : undefined
            }
            prefetch={false}
          >
            {t("actions.next")}
          </Link>
        </div>
      )}
    </>
  );
}
