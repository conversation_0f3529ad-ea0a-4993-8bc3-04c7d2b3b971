import { Suspense } from "react";
import { getTranslations } from "next-intl/server";
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import Text from "@/components/Text";
import { getUsersData } from "./controller";
import UserTableClient from "./_components/UserTableClient";
import { userPaginationLoader } from "./userSearchParams";
import UsersTable from "./_components/UsersTable";

interface UsersPageProps {
  params: Promise<{ [key: string]: string | string[] | undefined }>;
}

async function LoadingUsersFallback() {
  const t = await getTranslations("CMS.users");
  return <Text>{t("loading")}</Text>;
}

export default async function CMSUsersPage({ params }: UsersPageProps) {
  const t = await getTranslations("CMS.users");

  const parsedParams = userPaginationLoader(await params);

  const { users, total, page, limit } = await getUsersData(parsedParams);

  return (
    <section className="flex flex-1 flex-col gap-6">
      <header>
        <Text as="h1" size="2xl" className="font-bold tracking-tight">
          {t("title")}
        </Text>
        <Text as="p" size="sm" className="text-muted-foreground mt-1">
          {t("subtitle")}
        </Text>
      </header>
      <Card>
        <CardHeader>
          <CardTitle className="text-xl">{t("table.userList")}</CardTitle>
        </CardHeader>
        <CardContent className="p-4 sm:p-6">
          <Suspense fallback={<LoadingUsersFallback />}>
            <UsersTable
              initialUsers={users}
              totalUsers={total}
              initialPage={page}
              initialLimit={limit}
            />
          </Suspense>
        </CardContent>
      </Card>
    </section>
  );
}
